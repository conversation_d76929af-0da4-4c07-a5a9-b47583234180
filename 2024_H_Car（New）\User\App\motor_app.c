/**
 * @file    motor_app.c
 * @brief   电机应用层控制模块
 * @description 智能小车双电机控制应用层实现，提供电机初始化和任务调度接口
 */
#include "motor_app.h"

/**
 * @brief 全局电机对象定义
 * @description 定义左右两个电机的控制对象，用于存储电机状态和控制参数
 */
MOTOR left_motor;   // 左电机控制对象：控制小车左轮运动
MOTOR right_motor;  // 右电机控制对象：控制小车右轮运动

/**
 * @brief 电机初始化函数
 * @description 初始化左右两个电机的硬件配置和控制参数
 * @retval None
 * @note 使用TIM1的两个通道分别控制左右电机的PWM输出
 */
void Motor_Init(void)
{
    // 初始化左电机配置
    // 参数：电机对象, 定时器句柄, PWM通道, 方向控制引脚1, 方向控制引脚2, 初始速度, 最大速度
    Motor_Config_Init(&left_motor, &htim1, TIM_CHANNEL_1,
                      AIN1_GPIO_Port, AIN1_Pin,     // 左电机方向控制引脚A
                      AIN2_GPIO_Port, AIN2_Pin,     // 左电机方向控制引脚B
                      0, 80);                       // 初始速度0，最大速度80%

    // 初始化右电机配置
    // 参数：电机对象, 定时器句柄, PWM通道, 方向控制引脚1, 方向控制引脚2, 初始速度, 最大速度
    Motor_Config_Init(&right_motor, &htim1, TIM_CHANNEL_2,
                      BIN1_GPIO_Port, BIN1_Pin,     // 右电机方向控制引脚A
                      BIN2_GPIO_Port, BIN2_Pin,     // 右电机方向控制引脚B
                      0, 80);                       // 初始速度0，最大速度80%
}

/**
 * @brief 电机任务函数
 * @description 电机控制的周期性任务，由任务调度器定期调用
 * @retval None
 * @note 当前为空实现，实际的电机控制逻辑在PID控制器中完成
 *       此函数可用于添加电机状态监控、故障检测等功能
 */
void Motor_Task(void)
{
    // 当前为空实现
    // 可在此添加：
    // 1. 电机状态监控
    // 2. 电机故障检测
    // 3. 电机温度保护
    // 4. 电机电流监测等功能
}
