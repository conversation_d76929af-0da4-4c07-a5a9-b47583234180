/**
 * @file    Scheduler.h
 * @brief   智能小车任务调度器头文件
 * @description 定义任务调度器的接口函数和相关声明
 *              提供基于时间片的多任务调度功能
 * @version 1.0
 * @date    2025
 */

#ifndef __SCHEDULER_H__  // 防止头文件重复包含的宏定义开始
#define __SCHEDULER_H__

#include "MyDefine.h"    // 包含项目通用定义和所有必要的头文件

/**
 * @brief 调度器初始化函数声明
 * @description 初始化任务调度器，计算任务数量并调用系统初始化
 * @param None
 * @retval None
 * @note 必须在main函数中，进入主循环前调用
 */
void Scheduler_Init(void);

/**
 * @brief 调度器运行函数声明
 * @description 任务调度器的核心运行函数，负责任务的时间管理和调度执行
 * @param None
 * @retval None
 * @note 应在main函数的while(1)循环中持续调用
 */
void Scheduler_Run(void);

#endif  // __SCHEDULER_H__ 防止头文件重复包含的宏定义结束
