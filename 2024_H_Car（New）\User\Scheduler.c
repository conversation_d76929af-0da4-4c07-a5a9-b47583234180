/**
 * @file    Scheduler.c
 * @brief   智能小车任务调度器实现
 * @description 基于时间片轮询的简单任务调度器，支持多任务按时间周期执行
 *              采用非抢占式调度，适用于实时性要求不高的嵌入式应用
 */
#include "Scheduler.h"

/**
 * @brief 任务控制块结构体定义
 * @description 每个任务的控制信息，包含任务函数指针、执行周期和时间记录
 */
typedef struct {
  void (*task_func)(void);  // 任务函数指针：指向要执行的任务函数
  uint32_t rate_ms;         // 执行周期（毫秒）：任务执行的时间间隔
  uint32_t last_run;        // 上次执行时间（毫秒）：记录任务上次执行的系统时间戳
} scheduler_task_t;

/**
 * @brief 全局变量：当前激活的任务数量
 * @description 存储任务数组中实际有效的任务个数，由Scheduler_Init()计算得出
 */
uint8_t task_num;

/**
 * @brief 静态任务数组：系统中所有任务的配置表
 * @description 每个任务包含：{任务函数, 执行周期(ms), 上次运行时间(初始为0)}
 *              注释掉的任务表示暂时未启用，可根据需要开启
 */
static scheduler_task_t scheduler_task[] =
{
  {Led_Task, 1, 0},        // LED任务：1ms周期执行，用于状态指示灯控制
//  {Key_Task, 10, 0},       // 按键任务：10ms周期执行，用于按键扫描和处理
  {Oled_Task, 10, 0},      // OLED任务：10ms周期执行，用于显示屏刷新
//  {Uart_Task, 10, 0},      // 串口任务：10ms周期执行，用于串口通信处理
//  {Mpu6050_Task, 1, 0},    // MPU6050任务：1ms周期执行，用于陀螺仪数据读取
//  {Gray_Task, 10, 0},      // 灰度传感器任务：10ms周期执行，用于循迹检测
//  {Motor_Task, 10, 0},     // 电机任务：10ms周期执行，用于电机控制
//  {Encoder_Task, 10, 0},   // 编码器任务：10ms周期执行，用于速度反馈
};


/**
 * @brief 调度器初始化函数
 * @description 初始化任务调度器，计算有效任务数量并调用系统初始化
 * @retval None
 * @note 必须在main函数中调用，在进入主循环之前完成初始化
 */
void Scheduler_Init(void)
{
  System_Init();  // 调用系统初始化函数，初始化各个模块

  // 计算任务数组的元素个数，并将结果存储在 task_num 中
  task_num = sizeof(scheduler_task) / sizeof(scheduler_task_t); // 数组总字节数 / 单个元素字节数 = 元素个数
}

/**
 * @brief 调度器运行函数 - 核心调度逻辑
 * @description 遍历所有任务，检查执行时间并调度任务执行
 *              采用时间片轮询方式，非抢占式调度
 * @retval None
 * @note 此函数在main函数的while(1)循环中被持续调用
 * @algorithm 调度算法说明：
 *            1. 遍历任务数组中的每个任务
 *            2. 获取当前系统时间戳
 *            3. 判断是否到达任务的执行时间点
 *            4. 如果到时间则执行任务并更新时间戳
 */
void Scheduler_Run(void)
{
  // 遍历任务数组中的所有激活任务
  for (uint8_t i = 0; i < task_num; i++)
  {
    // 获取当前的系统时间（毫秒）- HAL_GetTick()返回系统启动后的毫秒数
    uint32_t now_time = HAL_GetTick();

    // 检查当前时间是否达到任务的执行时间
    // 条件：当前时间 >= (任务周期 + 上次执行时间)
    if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
    {
      // 更新任务的上次运行时间为当前时间，为下次调度做准备
      scheduler_task[i].last_run = now_time;

      // 执行任务函数 - 调用任务函数指针指向的具体任务
      scheduler_task[i].task_func();
    }
  }
}
