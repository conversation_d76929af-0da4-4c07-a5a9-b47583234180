/**
 * @file    pid_app.c
 * @brief   PID控制器应用层实现
 * @description 智能小车多环PID控制系统，包含速度环、循迹环和角度环控制
 *              实现差速转向和精确路径跟踪功能
 */
#include "pid_app.h"

// 外部变量声明：编码器对象
extern Encoder left_encoder;   // 左轮编码器，提供速度反馈
extern Encoder right_encoder;  // 右轮编码器，提供速度反馈

// 外部变量声明：电机对象
extern MOTOR left_motor;   // 左电机控制对象
extern MOTOR right_motor;  // 右电机控制对象

/**
 * @brief 基础速度设定
 * @description 小车的基准运行速度，其他控制环在此基础上进行调节
 */
int basic_speed = 130; // 基础速度：130 cm/s

/**
 * @brief PID控制器实例定义
 * @description 定义四个PID控制器，构成多环控制系统
 */
PID_T pid_speed_left;   // 左轮速度环PID：控制左轮转速
PID_T pid_speed_right;  // 右轮速度环PID：控制右轮转速
PID_T pid_line;         // 循迹环PID：根据灰度传感器进行路径跟踪
PID_T pid_angle;        // 角度环PID：根据陀螺仪进行角度控制

/**
 * @brief PID参数配置结构体
 * @description 为每个PID控制器定义专门的参数，经过实际调试优化
 */

// 左轮速度环PID参数：注重快速响应和稳定性
PidParams_t pid_params_left = {
    .kp = 15.0f,        // 比例系数：快速响应速度误差
    .ki = 0.1000f,      // 积分系数：消除稳态误差
    .kd = 0.00f,        // 微分系数：速度环通常不需要微分
    .out_min = -999.0f, // 输出下限
    .out_max = 999.0f,  // 输出上限
};

// 右轮速度环PID参数：与左轮保持一致
PidParams_t pid_params_right = {
    .kp = 15.0f,        // 比例系数：与左轮相同
    .ki = 0.1000f,      // 积分系数：与左轮相同
    .kd = 0.00f,        // 微分系数：与左轮相同
    .out_min = -999.0f, // 输出下限
    .out_max = 999.0f,  // 输出上限
};

// 循迹环PID参数：注重快速纠偏和抗干扰
PidParams_t pid_params_line = {
    .kp = 10.0f,        // 比例系数：适中的响应速度
    .ki = 0.0000f,      // 积分系数：循迹通常不需要积分
    .kd = 160.00f,      // 微分系数：较大值提供预测性纠偏
    .out_min = -999.0f, // 输出下限
    .out_max = 999.0f,  // 输出上限
};

// 角度环PID参数：注重精确角度控制
PidParams_t pid_params_angle = {
    .kp = 1.2f,         // 比例系数：较小值避免震荡
    .ki = 0.0001f,      // 积分系数：很小的值消除角度漂移
    .kd = 10.00f,       // 微分系数：提供角速度阻尼
    .out_min = -999.0f, // 输出下限
    .out_max = 999.0f,  // 输出上限
};

/**
 * @brief PID控制器初始化函数
 * @description 初始化所有PID控制器的参数和目标值
 * @retval None
 * @note 必须在系统初始化阶段调用，为PID控制做准备
 */
void PID_Init(void)
{
  // 初始化左轮速度环PID控制器
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,  // PID参数
           0.0f, pid_params_left.out_max);                              // 目标值和输出限幅

  // 初始化右轮速度环PID控制器
  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd, // PID参数
           0.0f, pid_params_right.out_max);                               // 目标值和输出限幅

  // 初始化循迹环PID控制器
  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,  // PID参数
           0.0f, pid_params_line.out_max);                              // 目标值和输出限幅

  // 初始化角度环PID控制器
  pid_init(&pid_angle,
           pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd, // PID参数
           0.0f, pid_params_angle.out_max);                               // 目标值和输出限幅

  // 设置各PID控制器的初始目标值
  pid_set_target(&pid_speed_left, basic_speed);  // 左轮目标速度：基础速度
  pid_set_target(&pid_speed_right, basic_speed); // 右轮目标速度：基础速度
  pid_set_target(&pid_line, 0);                  // 循迹目标：中心线位置
  pid_set_target(&pid_angle, 0);                 // 角度目标：0度（直行）
}

/**
 * @brief PID控制系统运行状态标志
 * @description 控制整个PID系统的启停，false=停止，true=运行
 */
bool pid_running = false; // PID控制使能开关：默认关闭

/**
 * @brief PID控制模式选择
 * @description 选择当前使用的控制策略
 *              0 = 角度环控制（用于转弯和角度保持）
 *              1 = 循迹环控制（用于沿线行驶）
 */
unsigned char pid_control_mode = 0; // 控制模式：默认角度环控制

/**
 * @brief 循迹环控制函数
 * @description 基于灰度传感器的循迹控制，实现沿线行驶
 * @retval None
 * @algorithm 控制算法：
 *            1. 根据灰度传感器计算线条位置误差
 *            2. 通过PID算法计算转向修正量
 *            3. 将修正量作用到左右轮速度差上实现转向
 */
void Line_PID_control(void) // 循迹环控制
{
  int line_pid_output = 0;  // 循迹PID输出值

  // 使用位置式PID计算循迹修正量
  // 输入：g_line_position_error（线条位置误差，由灰度传感器计算得出）
  line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);

  // 输出限幅：防止输出值超出设定范围
  line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);

  // 差速控制：将PID输出作用到左右轮速度目标值上
  // 左轮减速，右轮加速 → 左转；左轮加速，右轮减速 → 右转
  pid_set_target(&pid_speed_left, basic_speed - line_pid_output);   // 左轮目标速度
  pid_set_target(&pid_speed_right, basic_speed + line_pid_output);  // 右轮目标速度
}

/**
 * @brief 角度环控制函数
 * @description 基于陀螺仪的角度控制，实现精确转向和角度保持
 * @retval None
 * @algorithm 控制算法：
 *            1. 读取当前偏航角Yaw
 *            2. 通过PID算法计算角度修正量
 *            3. 将修正量作用到左右轮速度差上实现角度调节
 */
void Angle_PID_control(void) // 角度环控制
{
  int angle_pid_output = 0;  // 角度PID输出值

  // 使用位置式PID计算角度修正量
  // 输入：Yaw（当前偏航角，由MPU6050陀螺仪提供）
  angle_pid_output = pid_calculate_positional(&pid_angle, Yaw);

  // 输出限幅：防止输出值超出设定范围
  angle_pid_output = pid_constrain(angle_pid_output, pid_params_angle.out_min, pid_params_angle.out_max);

  // 差速控制：将PID输出作用到左右轮速度目标值上
  // 实现角度纠偏：角度偏差越大，左右轮速度差越大
  pid_set_target(&pid_speed_left, basic_speed - angle_pid_output);   // 左轮目标速度
  pid_set_target(&pid_speed_right, basic_speed + angle_pid_output);  // 右轮目标速度
}

/**
 * @brief PID控制任务主函数
 * @description 多环PID控制系统的核心调度函数，由任务调度器定期调用
 * @retval None
 * @note 此函数每5ms被调用一次，实现实时控制
 * @algorithm 控制流程：
 *            1. 检查PID系统是否使能
 *            2. 根据控制模式选择循迹环或角度环
 *            3. 执行速度环PID控制
 *            4. 将控制输出发送给电机
 */
void PID_Task(void)
{
  if(pid_running == false) return;  // PID系统未使能则直接返回

  int output_left = 0, output_right = 0;  // 左右电机的最终输出值

  // 根据控制模式选择不同的控制策略
  if(pid_control_mode == 0) // 角度环控制模式
  {
    // 根据行驶距离动态调整速度：长距离行驶时减速提高稳定性
    if(distance > 20000) // 超过20米时减速
      basic_speed = 75;   // 降低到75 cm/s
    else
      basic_speed = 130;  // 保持130 cm/s

    Angle_PID_control();  // 执行角度环控制
  }
  else // 循迹环控制模式
  {
    basic_speed = 94;     // 循迹时使用较低速度提高精度
    Line_PID_control();   // 执行循迹环控制
  }

  // 速度环PID控制：将目标速度转换为电机控制量
  // 输入：编码器反馈的实际速度 (cm/s)
  // 输出：电机PWM控制量
  output_left = pid_calculate_positional(&pid_speed_left, left_encoder.speed_cm_s);    // 左轮速度环
  output_right = pid_calculate_positional(&pid_speed_right, right_encoder.speed_cm_s); // 右轮速度环

  // 输出限幅：确保电机控制量在安全范围内
  output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
  output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);

  // 将PID输出发送给电机驱动器
  Motor_Set_Speed(&left_motor, output_left);   // 设置左电机速度
  Motor_Set_Speed(&right_motor, output_right); // 设置右电机速度
}

