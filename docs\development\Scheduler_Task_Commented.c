/**
 * @file    Scheduler_Task.c
 * @brief   2024年全国大学生智能汽车竞赛H组 - 循迹小车任务实现
 * @description 基于STM32F407的智能循迹小车控制系统
 * @hardware 硬件配置清单:
 *           - 减速电机编码器 * 2 (左右轮速度反馈)
 *           - TB6612电机驱动模块 (双路电机驱动)
 *           - 12V降压模块 (电源管理)
 *           - 华为8路灰度传感器阵列 (循迹检测)
 *           - MPU6050六轴传感器 (姿态检测)
 *           - 0.96寸OLED显示屏 (状态显示)
 * @version 1.0
 * @date    2025
 */

#include "Scheduler_Task.h"

/**
 * @brief 系统初始化函数
 * @description 按顺序初始化所有硬件模块和软件组件
 * @retval None
 * @note 初始化顺序很重要，某些模块依赖于其他模块的先行初始化
 */
void System_Init(void)
{
  Led_Init();      // 初始化LED状态指示灯
  Key_Init();      // 初始化按键输入检测
  Oled_Init();     // 初始化OLED显示屏
  Uart_Init();     // 初始化串口通信
  Gray_Init();     // 初始化灰度传感器阵列
  Motor_Init();    // 初始化电机驱动系统
  Encoder_Init();  // 初始化编码器速度反馈
  Mpu6050_Init();  // 初始化MPU6050陀螺仪
  PID_Init();      // 初始化PID控制器参数
  Uart_Printf(&huart1, "=== System Init ===\r\n");  // 发送初始化完成信息
  HAL_TIM_Base_Start_IT(&htim2);  // 启动定时器2中断，开始系统定时任务
}

// 外部变量声明：左右编码器对象
extern Encoder left_encoder;   // 左轮编码器，用于速度反馈
extern Encoder right_encoder;  // 右轮编码器，用于速度反馈

// 外部变量声明：左右电机对象
extern MOTOR left_motor;   // 左电机控制对象
extern MOTOR right_motor;  // 右电机控制对象

// 定时器变量：用于各种定时功能
unsigned char measure_timer5ms;   // 5ms测量定时器，用于传感器数据采集
unsigned char key_timer10ms;      // 10ms按键定时器，用于按键扫描

// 输出检测相关变量
unsigned char output_ff_flag;     // 输出检测标志位
unsigned int intput_timer500ms;   // 输入定时器500ms

// 输入检测相关变量  
unsigned char intput_ff_flag;     // 输入检测标志位
unsigned int output_timer500ms;   // 输出定时器500ms

unsigned int led_timer500ms;      // LED定时器：每检测到一个点，LED闪烁500ms

unsigned char point_count = 0;    // 检测到的点位数量（进圈+1，出圈+1）

unsigned char system_mode = 4;    // 系统状态：1~4对应4个题目

unsigned char circle_count = 0;   // 已完成圈数计数器

unsigned int distance = 0;        // 记录小车每一段行驶的距离

/**
 * @brief TIM2中断服务函数 - 1ms中断
 * @param htim: 定时器句柄指针
 * @description 系统核心定时中断，负责各种定时任务的调度
 *              包括按键扫描、传感器读取、PID控制等
 */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance != htim2.Instance) return;  // 确保是TIM2中断
 
  /* 10ms按键扫描任务 */
  if(++key_timer10ms >= 10)  // 每10ms执行一次
  {
    key_timer10ms = 0;       // 重置计数器
    Key_Task();              // 执行按键扫描任务
  }
  
  /* 5ms测量任务 */
  if(++measure_timer5ms >= 5)   // 每5ms执行一次
  {
    measure_timer5ms = 0;       // 重置计数器
    Encoder_Task();             // 读取编码器数据
    distance += left_encoder.speed_cm_s;  // 累计行驶距离
    Mpu6050_Task();             // 读取陀螺仪数据
    Gray_Task();                // 读取灰度传感器数据
    PID_Task();                 // 执行PID控制算法
  }
  
  /* 进圈检测逻辑 */
  if(Digtal != 0x00)  // 当灰度传感器检测到线条时
  {
    output_ff_flag = 1;  // 设置输出标志
    if(++intput_timer500ms >= 800) intput_timer500ms = 800;  // 防止溢出
  }
  else if(output_ff_flag == 1 && intput_timer500ms == 800)  // 检测到完整的进圈过程
  {
    output_ff_flag = 0;      // 清除标志
    intput_timer500ms = 0;   // 重置计数器
    point_count++;           // 点位计数+1
    Car_State_Update();      // 更新小车状态
  }
  
  /* 出圈检测逻辑 */
  if(Digtal == 0x00)  // 当灰度传感器检测不到线条时
  {
    intput_ff_flag = 1;  // 设置输入标志
    if(++output_timer500ms >= 800) output_timer500ms = 800;  // 防止溢出
  }
  else if(intput_ff_flag == 1 && output_timer500ms == 800)  // 检测到完整的出圈过程
  {
    intput_ff_flag = 0;      // 清除标志
    output_timer500ms = 0;   // 重置计数器
    point_count++;           // 点位计数+1
    Car_State_Update();      // 更新小车状态
  }
  
  /* LED状态指示 */
  if(led_state == 1 && ++led_timer500ms >= 500)  // LED闪烁500ms
  {
    led_state = 0;           // 关闭LED
    led_timer500ms = 0;      // 重置计数器
  }
}

/**
 * @brief 小车状态更新函数
 * @description 每当检测到点位变化时，根据当前系统模式同步更新小车运行状态
 *              包括PID控制模式切换、目标角度设置、停车控制等
 * @retval None
 */
void Car_State_Update(void)
{
  led_state = 1;    // 点亮LED指示检测到点位
  distance = 0;     // 重置距离计数器
  
  switch(system_mode)  // 根据系统模式执行不同的控制逻辑
  {
    case 1: // 第一题：直线行驶 A -> B
      if(point_count == 1)  // 到达终点B
      {
        pid_running = 0;                // 停止PID控制
        Motor_Brake(&left_motor);       // 左电机刹车
        Motor_Brake(&right_motor);      // 右电机刹车
      }
      break;
      
    case 2: // 第二题：绕行一圈 A -> B -> C -> D
      if(point_count == 1)              // 到达点B
        pid_control_mode = 1;           // 使用循迹控制器
      else if(point_count == 2)         // 到达点C
      {
        pid_control_mode = 0;           // 使用角度环控制器
        pid_set_target(&pid_angle, -176);  // 设置目标角度-176度
      }
      else if(point_count == 3)         // 到达点D
        pid_control_mode = 1;           // 使用循迹控制器
      else if(point_count == 4)         // 回到起点A
      {
        pid_running = 0;                // 停止PID控制
        Motor_Brake(&left_motor);       // 左电机刹车
        Motor_Brake(&right_motor);      // 右电机刹车
      }
      break;
      
    case 3: // 第三题：8字绕行一圈 A -> C -> B -> D
      if(point_count == 1)              // 到达点C
      {
        pid_control_mode = 1;           // 使用循迹控制器
      }
      else if(point_count == 2)         // 到达点B
      {
        pid_control_mode = 0;           // 使用角度环控制器
        pid_set_target(&pid_angle, 253);   // 设置目标角度253度
      }
      else if(point_count == 3)         // 到达点D
      {
        pid_control_mode = 1;           // 使用循迹控制器
      }
      else if(point_count == 4)         // 回到起点A
      {
        pid_running = 0;                // 停止PID控制
        Motor_Brake(&left_motor);       // 左电机刹车
        Motor_Brake(&right_motor);      // 右电机刹车
      }
      break;
      
    case 4: // 第四题：8字绕行多圈
      if(point_count == 1)              // 到达点C
      {
        pid_control_mode = 1;           // 使用循迹控制器
      }
      else if(point_count == 2)         // 到达点B
      {
        pid_control_mode = 0;           // 使用角度环控制器
        // 根据圈数调整角度补偿，减少累积误差
        pid_set_target(&pid_angle, 253 - (0.3 * circle_count));
      }
      else if(point_count == 3)         // 到达点D
        pid_control_mode = 1;           // 使用循迹控制器
      else if(point_count == 4)         // 回到起点A
      {
        if(++circle_count >= 4)         // 完成4圈后停止
        {
          pid_running = 0;              // 停止PID控制
          Motor_Brake(&left_motor);     // 左电机刹车
          Motor_Brake(&right_motor);    // 右电机刹车
        }
        point_count = 0;                // 重置点位计数器
        pid_control_mode = 0;           // 使用角度环控制器
        // 根据圈数调整角度补偿，减少累积误差
        pid_set_target(&pid_angle, 0 - (0.2 * circle_count));
      }
      break;
  }
  
  /* 清除PID历史误差 */
  pid_reset(&pid_line);   // 重置循迹PID控制器
  pid_reset(&pid_angle);  // 重置角度PID控制器
}
