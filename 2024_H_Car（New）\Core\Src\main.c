/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : STM32F407智能小车主程序 - 程序入口点和系统初始化
  * @description    : 本文件包含智能小车的主要控制逻辑，包括系统初始化、
  *                   外设配置和任务调度器的启动
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"          // 主头文件，包含系统配置和函数声明
#include "dma.h"           // DMA直接内存访问配置
#include "i2c.h"           // I2C通信协议配置（用于MPU6050等传感器）
#include "tim.h"           // 定时器配置（用于PWM输出和编码器输入）
#include "usart.h"         // 串口通信配置（用于调试和数据传输）
#include "gpio.h"          // GPIO通用输入输出配置

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "MyDefine.h"      // 用户自定义头文件，包含项目特定的宏定义和声明
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
// 用户自定义类型定义区域（当前为空）
/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
// 用户自定义宏定义区域（当前为空）
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
// 用户自定义宏函数区域（当前为空）
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
// 私有变量声明区域（当前为空）

/* USER CODE BEGIN PV */
// 用户自定义私有变量区域（当前为空）
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);  // 系统时钟配置函数声明
/* USER CODE BEGIN PFP */
// 用户自定义函数原型声明区域（当前为空）
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
// 用户自定义代码区域（当前为空）
/* USER CODE END 0 */

/**
  * @brief  智能小车应用程序入口点
  * @retval int 程序返回值（实际上永不返回）
  * @description 主函数负责系统初始化和启动任务调度器
  */
int main(void)
{
  /* USER CODE BEGIN 1 */
  // 用户代码区域1：主函数开始前的初始化代码（当前为空）
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/
  // 微控制器配置开始

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();  // HAL库初始化：复位所有外设，初始化Flash接口和系统滴答定时器

  /* USER CODE BEGIN Init */
  // 用户代码区域：HAL初始化后的自定义初始化代码（当前为空）
  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();  // 配置系统时钟：设置CPU主频为168MHz

  /* USER CODE BEGIN SysInit */
  // 用户代码区域：系统初始化后的自定义代码（当前为空）
  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  // 初始化所有配置的外设模块
  MX_GPIO_Init();         // 初始化GPIO：配置所有引脚的输入输出模式
  MX_DMA_Init();          // 初始化DMA：配置直接内存访问控制器
  MX_USART1_UART_Init();  // 初始化串口1：用于调试信息输出和通信
  MX_I2C1_Init();         // 初始化I2C1：用于MPU6050陀螺仪传感器通信
  MX_TIM1_Init();         // 初始化定时器1：用于电机PWM控制
  MX_I2C3_Init();         // 初始化I2C3：用于OLED显示屏通信
  MX_TIM3_Init();         // 初始化定时器3：用于编码器脉冲计数
  MX_TIM4_Init();         // 初始化定时器4：用于编码器脉冲计数
  MX_TIM2_Init();         // 初始化定时器2：用于系统定时功能
  MX_USART2_UART_Init();  // 初始化串口2：用于额外的通信功能

  /* USER CODE BEGIN 2 */
  Scheduler_Init();       // 初始化任务调度器：设置任务列表和调度参数
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  // 主循环：程序的核心运行逻辑
  while (1)  // 无限循环，程序永不退出
  {
    Scheduler_Run();  // 运行任务调度器：按时间片轮询执行各个任务
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    // 用户代码区域：主循环中的自定义代码（当前为空）
  }
  /* USER CODE END 3 */
}

/**
  * @brief 系统时钟配置函数
  * @retval None
  * @description 配置STM32F407的系统时钟，使CPU运行在168MHz主频
  *              使用外部8MHz晶振，通过PLL倍频到168MHz
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};  // 振荡器初始化结构体
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};  // 时钟初始化结构体

  /** Configure the main internal regulator output voltage
   *  配置主内部稳压器输出电压
   */
  __HAL_RCC_PWR_CLK_ENABLE();  // 使能电源管理时钟
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);  // 设置电压调节器为Scale1模式（高性能）

  /** Initializes the RCC Oscillators according to the specified parameters
   * in the RCC_OscInitTypeDef structure.
   * 根据RCC_OscInitTypeDef结构体中的参数初始化RCC振荡器
   */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;  // 选择外部高速振荡器(HSE)
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;                   // 使能HSE
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;               // 使能PLL锁相环
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;       // PLL时钟源选择HSE
  RCC_OscInitStruct.PLL.PLLM = 4;                            // PLL分频系数M=4 (8MHz/4=2MHz)
  RCC_OscInitStruct.PLL.PLLN = 168;                          // PLL倍频系数N=168 (2MHz*168=336MHz)
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;               // PLL分频系数P=2 (336MHz/2=168MHz)
  RCC_OscInitStruct.PLL.PLLQ = 4;                            // PLL分频系数Q=4 (用于USB等)
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)       // 配置振荡器
  {
    Error_Handler();  // 配置失败则进入错误处理
  }

  /** Initializes the CPU, AHB and APB buses clocks
   *  初始化CPU、AHB和APB总线时钟
   */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK    // 配置时钟类型
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;  // 系统时钟源选择PLL输出
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;         // AHB时钟不分频 (168MHz)
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;          // APB1时钟4分频 (42MHz)
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;          // APB2时钟2分频 (84MHz)

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)  // 配置时钟，Flash延迟5个周期
  {
    Error_Handler();  // 配置失败则进入错误处理
  }
}

/* USER CODE BEGIN 4 */
// 用户代码区域4：用户自定义函数实现区域（当前为空）
/* USER CODE END 4 */

/**
  * @brief  错误处理函数 - 系统发生错误时调用
  * @retval None
  * @description 当系统检测到错误时，程序会跳转到此函数
  *              禁用所有中断并进入死循环，防止系统继续运行
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  /* 用户可以在此添加自己的错误报告实现 */
  __disable_irq();  // 禁用所有中断，防止进一步的系统操作
  while (1)         // 进入死循环，系统停止运行
  {
    // 可以在此添加LED闪烁等错误指示代码
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  断言失败报告函数
  * @param  file: 发生断言错误的源文件名指针
  * @param  line: 发生断言错误的源代码行号
  * @retval None
  * @description 当assert_param断言失败时，报告错误发生的文件名和行号
  *              仅在调试模式下启用（USE_FULL_ASSERT宏定义时）
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* 用户可以在此添加自己的实现来报告文件名和行号 */
  /* 例如: printf("参数错误: 文件 %s 第 %d 行\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
