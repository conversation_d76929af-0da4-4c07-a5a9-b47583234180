/**
 * @file    motor_driver.c
 * @brief   电机驱动器底层实现
 * @description TB6612电机驱动模块的底层控制接口，支持PWM调速和方向控制
 *              提供电机初始化、速度控制、停止和刹车等基础功能
 */
#include "motor_driver.h"

/**
 * @brief 绝对值宏定义
 * @description 计算数值的绝对值，用于PWM占空比设置
 */
#define Motor_ABS(x) ((x) >= 0 ? (x) : -(x))

/**
 * @brief 电机配置初始化函数
 * @param motor: 电机对象指针
 * @param htim: 定时器句柄指针（用于PWM输出）
 * @param pwm_channel: PWM通道号
 * @param in1_port: 方向控制引脚1的GPIO端口
 * @param in1_pin: 方向控制引脚1的GPIO引脚号
 * @param in2_port: 方向控制引脚2的GPIO端口
 * @param in2_pin: 方向控制引脚2的GPIO引脚号
 * @param reverse: 电机反向标志（0=正常，1=反向）
 * @param dead_band_speed: 死区补偿速度值
 * @retval None
 * @description 配置电机的硬件参数并启动PWM输出
 */
void Motor_Config_Init(MOTOR* motor, TIM_HandleTypeDef *htim, uint32_t pwm_channel,
                       GPIO_TypeDef *in1_port, uint16_t in1_pin, GPIO_TypeDef *in2_port, uint16_t in2_pin,
                       unsigned char reverse, int dead_band_speed)
{
    // 配置电机硬件参数
    motor->config.htim = htim;                  // 绑定定时器句柄
    motor->config.pwm_channel = pwm_channel;    // 设置PWM通道
    motor->config.in1.port = in1_port;          // 设置方向控制引脚1端口
    motor->config.in1.pin = in1_pin;            // 设置方向控制引脚1引脚号
    motor->config.in2.port = in2_port;          // 设置方向控制引脚2端口
    motor->config.in2.pin = in2_pin;            // 设置方向控制引脚2引脚号
    motor->config.reverse = reverse;            // 设置电机反向标志

    // 初始化电机运行参数
    motor->dead_band_speed = dead_band_speed;   // 设置死区补偿值
    motor->speed = 0;                           // 初始速度为0

    // 设置初始方向：正转方向
    HAL_GPIO_WritePin(motor->config.in1.port, motor->config.in1.pin, GPIO_PIN_SET);    // IN1高电平
    HAL_GPIO_WritePin(motor->config.in2.port, motor->config.in2.pin, GPIO_PIN_RESET);  // IN2低电平

    // 启动PWM输出并设置初始占空比为0
    HAL_TIM_PWM_Start(motor->config.htim, motor->config.pwm_channel);                   // 启动PWM
    __HAL_TIM_SET_COMPARE(motor->config.htim, motor->config.pwm_channel, motor->speed); // 设置占空比
}

/**
 * @brief 电机速度限幅函数
 * @param motor: 电机对象指针
 * @param speed: 输入速度值
 * @param max_speed: 最大速度限制
 * @param min_speed: 最小速度限制
 * @retval int: 限幅后的速度值
 * @description 将输入速度限制在指定范围内，防止超出电机能力范围
 */
int Motor_Limit_Speed(MOTOR* motor, int speed, int max_speed, int min_speed)
{
    if(speed > max_speed)        // 超过最大值时
        speed = max_speed;       // 限制为最大值
    else if(speed < min_speed)   // 低于最小值时
        speed = min_speed;       // 限制为最小值

    return speed;                // 返回限幅后的速度
}

/**
 * @brief 电机死区补偿函数
 * @param motor: 电机对象指针
 * @retval int: 补偿后的速度值
 * @description 补偿电机的启动死区，确保小速度时电机能够正常转动
 *              当速度绝对值小于死区值时，自动补偿到死区边界
 */
int Motor_Dead_Compensation(MOTOR* motor)
{
  // 正向低速时补偿到正向死区边界
  if(motor->speed > 0 && motor->speed < motor->dead_band_speed)
    return motor->dead_band_speed;
  // 反向低速时补偿到反向死区边界
  else if(motor->speed < 0 && motor->speed > -motor->dead_band_speed)
    return -motor->dead_band_speed;
  // 速度足够大时不需要补偿
  else
    return motor->speed;
}


/**
 * @brief 电机速度控制函数
 * @param motor: 电机对象指针
 * @param speed: 目标速度值（正值=正转，负值=反转）
 * @retval None
 * @description 设置电机的转速和方向，包含限幅、死区补偿和方向控制
 * @algorithm 控制流程：
 *            1. 速度限幅处理
 *            2. 死区补偿处理
 *            3. 根据速度正负设置转向
 *            4. 设置PWM占空比
 */
void Motor_Set_Speed(MOTOR* motor, int speed)
{
    // 第一步：速度限幅处理
    // 将速度限制在定时器周期范围内（-Period ~ +Period）
    motor->speed = Motor_Limit_Speed(motor, speed, motor->config.htim->Init.Period, -(motor->config.htim->Init.Period));

    // 第二步：死区补偿处理
    // 补偿电机启动死区，确保低速时能正常转动
    motor->speed = Motor_Dead_Compensation(motor);

    // 第三步：根据速度正负设置电机转向
    if(motor->speed >= 0) // 正转方向
    {
        // 根据reverse标志设置方向控制引脚
        // reverse=0: IN1=高，IN2=低（正转）
        // reverse=1: IN1=低，IN2=高（正转，用于电机接线反向的情况）
        HAL_GPIO_WritePin(motor->config.in1.port, motor->config.in1.pin, motor->config.reverse == 0 ? GPIO_PIN_SET : GPIO_PIN_RESET);
        HAL_GPIO_WritePin(motor->config.in2.port, motor->config.in2.pin, motor->config.reverse == 0 ? GPIO_PIN_RESET : GPIO_PIN_SET);
    }
    else // 反转方向
    {
        // 反转时IN1和IN2的电平相反
        HAL_GPIO_WritePin(motor->config.in1.port, motor->config.in1.pin, motor->config.reverse == 0 ? GPIO_PIN_RESET : GPIO_PIN_SET);
        HAL_GPIO_WritePin(motor->config.in2.port, motor->config.in2.pin, motor->config.reverse == 0 ? GPIO_PIN_SET : GPIO_PIN_RESET);
    }

    // 第四步：设置PWM占空比控制电机转速
    // 使用速度的绝对值作为PWM占空比，转向已由IN1/IN2控制
    __HAL_TIM_SET_COMPARE(motor->config.htim, motor->config.pwm_channel, Motor_ABS(motor->speed));
}

// ���ֹͣ  
void Motor_Stop(MOTOR* motor)
{
    HAL_GPIO_WritePin(motor->config.in1.port, motor->config.in1.pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(motor->config.in2.port, motor->config.in2.pin, GPIO_PIN_RESET);

    motor->speed = 0;
    __HAL_TIM_SET_COMPARE(motor->config.htim, motor->config.pwm_channel, Motor_ABS(motor->speed));
}    

// ���ɲ��
void Motor_Brake(MOTOR* motor)
{
    HAL_GPIO_WritePin(motor->config.in1.port, motor->config.in1.pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(motor->config.in2.port, motor->config.in2.pin, GPIO_PIN_SET);

    motor->speed = 0;
    __HAL_TIM_SET_COMPARE(motor->config.htim, motor->config.pwm_channel, Motor_ABS(motor->speed));
}
